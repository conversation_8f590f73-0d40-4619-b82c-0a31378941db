import { createClient } from '@/utils/supabase/server';
import { checkAdminStatus } from '@/app/admin/actions';

export default async function DebugAuthPage() {
  const supabase = await createClient();
  
  try {
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    // Check admin status
    const isAdmin = await checkAdminStatus();
    
    return (
      <div className="container mx-auto p-8">
        <h1 className="text-2xl font-bold mb-6">认证调试信息</h1>
        
        <div className="space-y-4">
          <div className="p-4 border rounded">
            <h2 className="font-semibold">认证状态</h2>
            <p>用户已登录: {user ? '是' : '否'}</p>
            {authError && <p className="text-red-500">认证错误: {authError.message}</p>}
          </div>
          
          {user && (
            <div className="p-4 border rounded">
              <h2 className="font-semibold">用户信息</h2>
              <p>用户ID: {user.id}</p>
              <p>邮箱: {user.email || '未设置'}</p>
              <p>创建时间: {user.created_at}</p>
              <p>最后登录: {user.last_sign_in_at || '未知'}</p>
            </div>
          )}
          
          <div className="p-4 border rounded">
            <h2 className="font-semibold">管理员权限</h2>
            <p>是否为管理员: {isAdmin ? '是' : '否'}</p>
            <p>管理员邮箱列表: <EMAIL></p>
            {user?.email && (
              <p>邮箱匹配: {user.email === '<EMAIL>' ? '匹配' : '不匹配'}</p>
            )}
          </div>
          
          <div className="p-4 border rounded bg-blue-50">
            <h2 className="font-semibold">解决方案</h2>
            {!user ? (
              <p>请先登录系统</p>
            ) : user.email !== '<EMAIL>' ? (
              <p>请使用 <EMAIL> 邮箱登录</p>
            ) : (
              <p>权限正常，可以访问管理页面</p>
            )}
          </div>
        </div>
        
        <div className="mt-6">
          <a 
            href="/admin" 
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            尝试访问管理页面
          </a>
        </div>
      </div>
    );
  } catch (error) {
    return (
      <div className="container mx-auto p-8">
        <h1 className="text-2xl font-bold mb-6 text-red-500">调试错误</h1>
        <p>发生错误: {error instanceof Error ? error.message : '未知错误'}</p>
      </div>
    );
  }
}
